<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="icon" type="image/png" sizes="32x32" href="./images/favicon-32x32.png">
  <title>Blogr - A modern publishing platform</title>

  <!-- Google Fonts -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Overpass:wght@300;600&family=Ubuntu:wght@400;500;700&display=swap" rel="stylesheet">

  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Overpass', sans-serif;
      font-size: 16px;
      line-height: 1.6;
      color: hsl(207, 13%, 34%);
      overflow-x: hidden;
    }

    /* Header and Navigation */
    .header {
      background: linear-gradient(135deg, hsl(13, 100%, 72%), hsl(353, 100%, 62%));
      border-radius: 0 0 0 100px;
      position: relative;
      overflow: hidden;
      min-height: 80vh;
    }

    .header::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('./images/bg-pattern-intro-desktop.svg') no-repeat center;
      background-size: 200% 200%;
      background-position: 25% 50%;
      opacity: 0.4;
    }

    .nav {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 3.5rem 10% 0;
      position: relative;
      z-index: 10;
    }

    .logo {
      font-family: 'Ubuntu', sans-serif;
      font-size: 2.2rem;
      font-weight: 700;
      color: white;
      text-decoration: none;
    }

    .nav-menu {
      display: flex;
      list-style: none;
      gap: 2.5rem;
      margin-left: 4rem;
    }

    .nav-item {
      position: relative;
    }

    .nav-link {
      color: white;
      text-decoration: none;
      font-weight: 400;
      font-size: 1rem;
      padding: 0.5rem 0;
      transition: all 0.3s ease;
      opacity: 0.9;
    }

    .nav-link:hover {
      text-decoration: underline;
      opacity: 1;
    }

    .nav-auth {
      display: flex;
      gap: 2rem;
      align-items: center;
    }

    .btn-login {
      color: white;
      text-decoration: none;
      font-weight: 600;
      font-size: 1rem;
      opacity: 0.9;
    }

    .btn-login:hover {
      opacity: 1;
      text-decoration: underline;
    }

    .btn-signup {
      background: white;
      color: hsl(356, 100%, 66%);
      text-decoration: none;
      font-family: 'Ubuntu', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      padding: 1rem 2.5rem;
      border-radius: 30px;
      transition: all 0.3s ease;
    }

    .btn-signup:hover {
      background: rgba(255, 255, 255, 0.3);
      color: white;
    }

    /* Hero Section */
    .hero {
      text-align: center;
      padding: 6rem 10% 8rem;
      position: relative;
      z-index: 10;
    }

    .hero h1 {
      font-family: 'Overpass', sans-serif;
      font-size: 4rem;
      font-weight: 600;
      color: white;
      margin-bottom: 1.5rem;
      line-height: 1.1;
      letter-spacing: -0.02em;
    }

    .hero p {
      font-size: 1.3rem;
      color: rgba(255, 255, 255, 0.9);
      margin-bottom: 3rem;
      font-weight: 300;
      line-height: 1.4;
    }

    .hero-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
    }

    .btn-primary {
      background: white;
      color: hsl(356, 100%, 66%);
      text-decoration: none;
      font-family: 'Ubuntu', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      padding: 1rem 2.5rem;
      border-radius: 30px;
      transition: all 0.3s ease;
    }

    .btn-primary:hover {
      background: hsl(355, 100%, 74%);
      color: white;
    }

    .btn-secondary {
      background: transparent;
      color: white;
      text-decoration: none;
      font-family: 'Ubuntu', sans-serif;
      font-weight: 700;
      font-size: 1rem;
      padding: 1rem 2.5rem;
      border: 1px solid white;
      border-radius: 30px;
      transition: all 0.3s ease;
    }

    .btn-secondary:hover {
      background: white;
      color: hsl(356, 100%, 66%);
    }

    /* Main Content Sections */
    .section {
      padding: 10rem 0;
    }

    .section-title {
      font-family: 'Overpass', sans-serif;
      font-size: 2.5rem;
      font-weight: 600;
      color: hsl(208, 49%, 24%);
      text-align: center;
      margin-bottom: 6rem;
    }

    /* Future Section */
    .future-section {
      position: relative;
      overflow: hidden;
      padding: 10rem 0 0;
    }

    .future-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
    }

    .future-text {
      padding: 0 0 0 10%;
      max-width: 500px;
    }

    .future-text h3 {
      font-family: 'Overpass', sans-serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: hsl(208, 49%, 24%);
      margin-bottom: 2rem;
      line-height: 1.2;
    }

    .future-text p {
      margin-bottom: 4rem;
      line-height: 1.7;
      color: hsl(207, 13%, 34%);
      font-size: 1rem;
    }

    .future-image {
      position: relative;
      overflow: visible;
    }

    .future-image img {
      width: 150%;
      height: auto;
      margin-left: 5rem;
      position: relative;
      z-index: 1;
    }

    /* Infrastructure Section */
    .infrastructure-section {
      background: linear-gradient(135deg, hsl(237, 17%, 21%), hsl(237, 23%, 31%));
      border-radius: 0 100px 0 100px;
      position: relative;
      overflow: hidden;
      margin: 15rem 0;
      padding: 0;
      min-height: 400px;
    }

    .infrastructure-section::before {
      content: '';
      position: absolute;
      top: -50%;
      left: -20%;
      right: 0;
      bottom: 0;
      background: url('./images/bg-pattern-circles.svg') no-repeat;
      background-size: 100%;
      opacity: 0.8;
    }

    .infrastructure-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0;
      align-items: center;
      position: relative;
      z-index: 2;
      max-width: 1200px;
      margin: 0 auto;
      padding: 6rem 0;
    }

    .infrastructure-image {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .infrastructure-image img {
      width: 100%;
      max-width: 400px;
      height: auto;
      position: relative;
      z-index: 3;
      margin-top: -6rem;
    }

    .infrastructure-text {
      color: white;
      padding: 0 10% 0 2rem;
    }

    .infrastructure-text h2 {
      font-family: 'Overpass', sans-serif;
      font-size: 2.5rem;
      font-weight: 600;
      margin-bottom: 2rem;
      line-height: 1.2;
    }

    .infrastructure-text p {
      font-size: 1rem;
      line-height: 1.7;
      opacity: 0.9;
    }

    /* Features Section */
    .features-section {
      position: relative;
      overflow: hidden;
      padding: 10rem 0;
    }

    .features-content {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 0;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
    }

    .features-image {
      order: -1;
      position: relative;
    }

    .features-image img {
      width: 150%;
      height: auto;
      margin-left: -8rem;
      position: relative;
      z-index: 1;
    }

    .features-text {
      padding: 0 10% 0 2rem;
      max-width: 500px;
    }

    .features-text h3 {
      font-family: 'Overpass', sans-serif;
      font-size: 1.8rem;
      font-weight: 600;
      color: hsl(208, 49%, 24%);
      margin-bottom: 2rem;
      line-height: 1.2;
    }

    .features-text p {
      margin-bottom: 4rem;
      line-height: 1.7;
      color: hsl(207, 13%, 34%);
      font-size: 1rem;
    }

    /* Footer */
    .footer {
      background: hsl(240, 10%, 16%);
      border-radius: 0 100px 0 0;
      padding: 4rem 0;
      margin-top: 8rem;
    }

    .footer-content {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr;
      gap: 8rem;
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 10%;
    }

    .footer-logo {
      font-family: 'Ubuntu', sans-serif;
      font-size: 2.2rem;
      font-weight: 700;
      color: white;
      text-decoration: none;
    }

    .footer-section h4 {
      color: white;
      font-family: 'Ubuntu', sans-serif;
      font-weight: 500;
      font-size: 1rem;
      margin-bottom: 2rem;
    }

    .footer-section ul {
      list-style: none;
    }

    .footer-section li {
      margin-bottom: 0.8rem;
    }

    .footer-section a {
      color: hsl(240, 2%, 79%);
      text-decoration: none;
      transition: color 0.3s ease;
      font-size: 0.95rem;
    }

    .footer-section a:hover {
      text-decoration: underline;
    }

    .attribution {
      font-size: 11px;
      text-align: center;
      margin-top: 2rem;
      color: hsl(207, 13%, 34%);
    }

    .attribution a {
      color: hsl(228, 45%, 44%);
    }

    /* Mobile Menu Toggle */
    .mobile-menu-toggle {
      display: none;
      background: none;
      border: none;
      cursor: pointer;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .nav-menu,
      .nav-auth {
        display: none;
      }

      .mobile-menu-toggle {
        display: block;
      }

      .nav {
        padding: 2rem 5% 0;
      }

      .hero {
        padding: 4rem 5% 6rem;
      }

      .hero h1 {
        font-size: 2.2rem;
      }

      .hero p {
        font-size: 1.1rem;
      }

      .hero-buttons {
        flex-direction: row;
        gap: 0.5rem;
      }

      .btn-primary,
      .btn-secondary {
        padding: 0.8rem 1.5rem;
        font-size: 0.9rem;
      }

      .section {
        padding: 6rem 5%;
      }

      .future-section {
        padding: 6rem 0 0;
      }

      .future-content,
      .infrastructure-content,
      .features-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
      }

      .future-text,
      .features-text {
        padding: 0 5%;
        max-width: none;
      }

      .future-image img,
      .features-image img {
        width: 100%;
        margin-left: 0;
      }

      .features-image {
        order: 0;
      }

      .infrastructure-section {
        margin: 8rem 0;
        padding: 0;
      }

      .infrastructure-content {
        padding: 4rem 0;
      }

      .infrastructure-text {
        padding: 0 5%;
      }

      .infrastructure-image img {
        max-width: 300px;
        margin-top: -3rem;
      }

      .footer {
        padding: 4rem 0;
      }

      .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 3rem;
        padding: 0 5%;
      }

      .section-title {
        font-size: 2rem;
        margin-bottom: 4rem;
      }

      .future-text h3,
      .features-text h3 {
        font-size: 1.5rem;
      }

      .infrastructure-text h2 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <!-- Header -->
  <header class="header">
    <nav class="nav">
      <a href="#" class="logo">Blogr</a>

      <ul class="nav-menu">
        <li class="nav-item">
          <a href="#" class="nav-link">Product</a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">Company</a>
        </li>
        <li class="nav-item">
          <a href="#" class="nav-link">Connect</a>
        </li>
      </ul>

      <div class="nav-auth">
        <a href="#" class="btn-login">Login</a>
        <a href="#" class="btn-signup">Sign Up</a>
      </div>

      <button class="mobile-menu-toggle">
        <img src="./images/icon-hamburger.svg" alt="Menu">
      </button>
    </nav>

    <div class="hero">
      <h1>A modern publishing platform</h1>
      <p>Grow your audience and build your online brand</p>
      <div class="hero-buttons">
        <a href="#" class="btn-primary">Start for Free</a>
        <a href="#" class="btn-secondary">Learn More</a>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main>
    <!-- Future Section -->
    <section class="section future-section">
      <h2 class="section-title">Designed for the future</h2>
      <div class="future-content">
        <div class="future-text">
          <h3>Introducing an extensible editor</h3>
          <p>Blogr features an exceedingly intuitive interface which lets you focus on one thing: creating content. The editor supports management of multiple blogs and allows easy manipulation of embeds such as images, videos, and Markdown. Extensibility with plugins and themes provide easy ways to add functionality or change the looks of a blog.</p>

          <h3>Robust content management</h3>
          <p>Flexible content management enables users to easily move through posts. Increase the usability of your blog by adding customized categories, sections, format, or flow. With this functionality, you’re in full control.</p>
        </div>
        <div class="future-image">
          <img src="./images/illustration-editor-desktop.svg" alt="Editor illustration">
        </div>
      </div>
    </section>

    <!-- Infrastructure Section -->
    <section class="section infrastructure-section">
      <div class="infrastructure-content">
        <div class="infrastructure-image">
          <img src="./images/illustration-phones.svg" alt="Phones illustration">
        </div>
        <div class="infrastructure-text">
          <h2>State of the Art Infrastructure</h2>
          <p>With reliability and speed in mind, worldwide data centers provide the backbone for ultra-fast connectivity. This ensures your site will load instantly, no matter where your readers are, keeping your site competitive.</p>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="section features-section">
      <div class="features-content">
        <div class="features-image">
          <img src="./images/illustration-laptop-desktop.svg" alt="Laptop illustration">
        </div>
        <div class="features-text">
          <h3>Free, open, simple</h3>
          <p>Blogr is a free and open source application backed by a large community of helpful developers. It supports features such as code syntax highlighting, RSS feeds, social media integration, third-party commenting tools, and works seamlessly with Google Analytics. The architecture is clean and is relatively easy to learn.</p>

          <h3>Powerful tooling</h3>
          <p>Batteries included. We built a simple and straightforward CLI tool that makes customization and deployment a breeze, but capable of producing even the most complicated sites.</p>
        </div>
      </div>
    </section>
  </main>

  <!-- Footer -->
  <footer class="footer">
    <div class="footer-content">
      <div>
        <a href="#" class="footer-logo">Blogr</a>
      </div>

      <div class="footer-section">
        <h4>Product</h4>
        <ul>
          <li><a href="#">Overview</a></li>
          <li><a href="#">Pricing</a></li>
          <li><a href="#">Marketplace</a></li>
          <li><a href="#">Features</a></li>
          <li><a href="#">Integrations</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h4>Company</h4>
        <ul>
          <li><a href="#">About</a></li>
          <li><a href="#">Team</a></li>
          <li><a href="#">Blog</a></li>
          <li><a href="#">Careers</a></li>
        </ul>
      </div>

      <div class="footer-section">
        <h4>Connect</h4>
        <ul>
          <li><a href="#">Contact</a></li>
          <li><a href="#">Newsletter</a></li>
          <li><a href="#">LinkedIn</a></li>
        </ul>
      </div>
    </div>

    <div class="attribution">
      Challenge by <a href="https://www.frontendmentor.io?ref=challenge" target="_blank">Frontend Mentor</a>.
      Coded by <a href="#">Your Name Here</a>.
    </div>
  </footer>
</body>
</html>