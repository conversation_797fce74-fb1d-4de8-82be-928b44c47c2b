# Front-end Style Guide

## Layout

The designs were created to the following widths:

- Mobile: 375px
- Desktop: 1440px

> 💡 These are just the design sizes. Ensure content is responsive and meets WCAG requirements by testing the full range of screen sizes from 320px to large screens.

## Colors

### Primary

- Red 500: hsl(356, 100%, 66%)
- Red 400: hsl(355, 100%, 74%)
- Blue 900: hsl(208, 49%, 24%)

### Neutral

- White: hsl(0, 100%, 100%)
- Gray 600: hsl(207, 13%, 34%)
- Gray 900: hsl(240, 10%, 16%)

### Gradient

Background gradient - intro/CTA mobile nav:

- Orange 300: hsl(13, 100%, 72%)
- Red 550: hsl(353, 100%, 62%)

Background gradient - body:

- Purple 950: hsl(237, 17%, 21%)
- Purple 900: hsl(237, 23%, 31%)

## Typography

### Body Copy

- Font size: 16px

### Fonts

- Family: [Overpass](https://fonts.google.com/specimen/Overpass?preview.text_type=custom)
- Weights: 300, 600

- Family: [Ubuntu](https://fonts.google.com/specimen/Ubuntu?preview.text_type=custom)
- Weights: 400, 500, 700

> 💎 [Upgrade to Pro](https://www.frontendmentor.io/pro?ref=style-guide) for design file access to see all design details and get hands-on experience using a professional workflow with tools like Figma.
